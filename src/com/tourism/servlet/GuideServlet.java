package com.tourism.servlet;

import com.tourism.dao.GuideDAO;
import com.tourism.model.Guide;
import com.tourism.util.PageUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 导游管理Servlet
 */
public class GuideServlet extends HttpServlet {
    private GuideDAO guideDAO = new GuideDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listGuides(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteGuide(request, response);
                break;
            case "search":
                searchGuides(request, response);
                break;
            default:
                listGuides(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addGuide(request, response);
        } else if ("update".equals(action)) {
            updateGuide(request, response);
        }
    }
    
    /**
     * 显示导游列表（带分页）
     */
    private void listGuides(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // 获取分页参数
        int currentPage = 1;
        int pageSize = 10;

        String pageParam = request.getParameter("page");
        String sizeParam = request.getParameter("size");

        if (pageParam != null && !pageParam.isEmpty()) {
            try {
                currentPage = Integer.parseInt(pageParam);
            } catch (NumberFormatException e) {
                currentPage = 1;
            }
        }

        if (sizeParam != null && !sizeParam.isEmpty()) {
            try {
                pageSize = Integer.parseInt(sizeParam);
                if (pageSize < 5) pageSize = 5;
                if (pageSize > 50) pageSize = 50;
            } catch (NumberFormatException e) {
                pageSize = 10;
            }
        }

        // 创建分页对象
        PageUtil pageUtil = new PageUtil(currentPage, pageSize);

        // 获取总记录数
        int totalRecords = guideDAO.getTotalCount();
        pageUtil.setTotalRecords(totalRecords);

        // 获取当前页数据
        List<Guide> guides = guideDAO.getAllGuides(pageUtil);

        // 设置请求属性
        request.setAttribute("guides", guides);
        request.setAttribute("pageUtil", pageUtil);
        request.getRequestDispatcher("/guide-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加导游表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/guide-add.jsp").forward(request, response);
    }
    
    /**
     * 添加导游
     */
    private void addGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String languages = request.getParameter("languages");
        String specialties = request.getParameter("specialties");
        String experienceStr = request.getParameter("experience");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        // 检查身份证号是否已存在
        if (guideDAO.isIdCardExists(idCard, 0)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        // 检查导游证号是否已存在
        if (guideDAO.isLicenseNumberExists(licenseNumber, 0)) {
            request.getSession().setAttribute("error", "导游证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        int experience = 0;
        try {
            if (experienceStr != null && !experienceStr.trim().isEmpty()) {
                experience = Integer.parseInt(experienceStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "从业年限必须是数字！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        Guide guide = new Guide(name, idCard, phone, email, licenseNumber);
        guide.setLanguages(languages);
        guide.setSpecialties(specialties);
        guide.setExperience(experience);
        guide.setStatus(status != null ? status : "在职");
        guide.setDescription(description);
        
        boolean success = guideDAO.addGuide(guide);
        
        if (success) {
            request.getSession().setAttribute("message", "导游添加成功！");
        } else {
            request.getSession().setAttribute("error", "导游添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 显示编辑导游表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Guide guide = guideDAO.getGuideById(id);
        request.setAttribute("guide", guide);
        request.getRequestDispatcher("/guide-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新导游信息
     */
    private void updateGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String languages = request.getParameter("languages");
        String specialties = request.getParameter("specialties");
        String experienceStr = request.getParameter("experience");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        // 检查身份证号是否已存在（排除当前记录）
        if (guideDAO.isIdCardExists(idCard, id)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        // 检查导游证号是否已存在（排除当前记录）
        if (guideDAO.isLicenseNumberExists(licenseNumber, id)) {
            request.getSession().setAttribute("error", "导游证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        int experience = 0;
        try {
            if (experienceStr != null && !experienceStr.trim().isEmpty()) {
                experience = Integer.parseInt(experienceStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "从业年限必须是数字！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        Guide guide = new Guide();
        guide.setId(id);
        guide.setName(name);
        guide.setIdCard(idCard);
        guide.setPhone(phone);
        guide.setEmail(email);
        guide.setLicenseNumber(licenseNumber);
        guide.setLanguages(languages);
        guide.setSpecialties(specialties);
        guide.setExperience(experience);
        guide.setStatus(status);
        guide.setDescription(description);
        
        boolean success = guideDAO.updateGuide(guide);
        
        if (success) {
            request.getSession().setAttribute("message", "导游信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "导游信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 删除导游
     */
    private void deleteGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = guideDAO.deleteGuide(id);
        
        if (success) {
            request.getSession().setAttribute("message", "导游删除成功！");
        } else {
            request.getSession().setAttribute("error", "导游删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 搜索导游（带分页）
     */
    private void searchGuides(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String status = request.getParameter("status");
        String language = request.getParameter("language");

        // 获取分页参数
        int currentPage = 1;
        int pageSize = 10;

        String pageParam = request.getParameter("page");
        String sizeParam = request.getParameter("size");

        if (pageParam != null && !pageParam.isEmpty()) {
            try {
                currentPage = Integer.parseInt(pageParam);
            } catch (NumberFormatException e) {
                currentPage = 1;
            }
        }

        if (sizeParam != null && !sizeParam.isEmpty()) {
            try {
                pageSize = Integer.parseInt(sizeParam);
                if (pageSize < 5) pageSize = 5;
                if (pageSize > 50) pageSize = 50;
            } catch (NumberFormatException e) {
                pageSize = 10;
            }
        }

        // 创建分页对象
        PageUtil pageUtil = new PageUtil(currentPage, pageSize);

        List<Guide> guides;
        int totalRecords;

        if (language != null && !language.trim().isEmpty()) {
            // 按语言搜索
            totalRecords = guideDAO.getCountByLanguage(language);
            pageUtil.setTotalRecords(totalRecords);
            guides = guideDAO.getGuidesByLanguage(language, pageUtil);
        } else if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            totalRecords = guideDAO.getCountByStatus(status);
            pageUtil.setTotalRecords(totalRecords);
            guides = guideDAO.getGuidesByStatus(status, pageUtil);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            totalRecords = guideDAO.getSearchCountByName(keyword);
            pageUtil.setTotalRecords(totalRecords);
            guides = guideDAO.searchGuidesByName(keyword, pageUtil);
        } else {
            // 显示所有
            totalRecords = guideDAO.getTotalCount();
            pageUtil.setTotalRecords(totalRecords);
            guides = guideDAO.getAllGuides(pageUtil);
        }

        request.setAttribute("guides", guides);
        request.setAttribute("pageUtil", pageUtil);
        request.setAttribute("keyword", keyword);
        request.setAttribute("status", status);
        request.setAttribute("language", language);
        request.getRequestDispatcher("/guide-list.jsp").forward(request, response);
    }
}
