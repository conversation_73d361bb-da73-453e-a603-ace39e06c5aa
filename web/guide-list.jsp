<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Guide" %>
<%@ page import="com.tourism.util.PageUtil" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导游管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .table-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .btn-group .btn { margin-right: 5px; }
        .search-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .status-badge { font-size: 0.8em; }
        .experience-badge { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
        .pagination-container { background: white; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); padding: 15px; margin-top: 20px; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-user-tie text-primary me-2"></i>导游管理</h2>
            <a href="${pageContext.request.contextPath}/guide?action=add" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加导游
            </a>
        </div>

        <!-- 消息提示 -->
        <%
            String message = (String) session.getAttribute("message");
            if (message != null) {
        %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><%= message %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <%
                session.removeAttribute("message");
            }
            
            String error = (String) session.getAttribute("error");
            if (error != null) {
        %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <%
                session.removeAttribute("error");
            }
        %>

        <!-- 搜索区域 -->
        <div class="search-container">
            <form method="get" action="${pageContext.request.contextPath}/guide">
                <input type="hidden" name="action" value="search">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">姓名搜索</label>
                        <input type="text" class="form-control" name="keyword" 
                               value="${param.keyword}" placeholder="输入导游姓名">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">状态筛选</label>
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="在职" ${param.status == '在职' ? 'selected' : ''}>在职</option>
                            <option value="离职" ${param.status == '离职' ? 'selected' : ''}>离职</option>
                            <option value="休假" ${param.status == '休假' ? 'selected' : ''}>休假</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">语言能力</label>
                        <select class="form-select" name="language">
                            <option value="">全部语言</option>
                            <option value="英文" ${param.language == '英文' ? 'selected' : ''}>英文</option>
                            <option value="日文" ${param.language == '日文' ? 'selected' : ''}>日文</option>
                            <option value="韩文" ${param.language == '韩文' ? 'selected' : ''}>韩文</option>
                            <option value="法文" ${param.language == '法文' ? 'selected' : ''}>法文</option>
                            <option value="德文" ${param.language == '德文' ? 'selected' : ''}>德文</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">每页显示</label>
                        <select class="form-select" name="size">
                            <option value="10" ${param.size == '10' ? 'selected' : ''}>10条</option>
                            <option value="20" ${param.size == '20' ? 'selected' : ''}>20条</option>
                            <option value="50" ${param.size == '50' ? 'selected' : ''}>50条</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                            <a href="${pageContext.request.contextPath}/guide" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 导游列表 -->
        <div class="table-container p-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>导游列表
                    <%
                        List<Guide> guides = (List<Guide>) request.getAttribute("guides");
                        if (guides != null) {
                    %>
                        <span class="badge bg-primary ms-2"><%= guides.size() %></span>
                    <%
                        }
                    %>
                </h5>
            </div>

            <%
                if (guides != null && !guides.isEmpty()) {
            %>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>导游证号</th>
                                <th>电话</th>
                                <th>语言能力</th>
                                <th>从业年限</th>
                                <th>专业特长</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <%
                                for (Guide guide : guides) {
                            %>
                                <tr>
                                    <td><%= guide.getId() %></td>
                                    <td>
                                        <strong><%= guide.getName() %></strong>
                                        <% if (guide.getEmail() != null && !guide.getEmail().isEmpty()) { %>
                                            <br><small class="text-muted"><%= guide.getEmail() %></small>
                                        <% } %>
                                    </td>
                                    <td><%= guide.getLicenseNumber() %></td>
                                    <td><%= guide.getPhone() %></td>
                                    <td>
                                        <% if (guide.getLanguages() != null && !guide.getLanguages().isEmpty()) { %>
                                            <span class="badge bg-info"><%= guide.getLanguages() %></span>
                                        <% } else { %>
                                            <span class="text-muted">未填写</span>
                                        <% } %>
                                    </td>
                                    <td>
                                        <span class="badge experience-badge"><%= guide.getExperience() %>年</span>
                                    </td>
                                    <td>
                                        <% if (guide.getSpecialties() != null && !guide.getSpecialties().isEmpty()) { %>
                                            <%= guide.getSpecialties().length() > 20 ? 
                                                guide.getSpecialties().substring(0, 20) + "..." : 
                                                guide.getSpecialties() %>
                                        <% } else { %>
                                            <span class="text-muted">未填写</span>
                                        <% } %>
                                    </td>
                                    <td>
                                        <%
                                            String statusClass = "secondary";
                                            if ("在职".equals(guide.getStatus())) {
                                                statusClass = "success";
                                            } else if ("离职".equals(guide.getStatus())) {
                                                statusClass = "danger";
                                            } else if ("休假".equals(guide.getStatus())) {
                                                statusClass = "warning";
                                            }
                                        %>
                                        <span class="badge bg-<%= statusClass %> status-badge">
                                            <%= guide.getStatus() %>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="${pageContext.request.contextPath}/guide?action=edit&id=<%= guide.getId() %>" 
                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" 
                                               onclick="confirmDelete(<%= guide.getId() %>, '<%= guide.getName() %>')" 
                                               class="btn btn-sm btn-outline-danger" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <%
                                }
                            %>
                        </tbody>
                    </table>
                </div>
            <%
                } else {
            %>
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无导游信息</h5>
                    <p class="text-muted">点击上方"添加导游"按钮开始添加导游信息</p>
                    <a href="${pageContext.request.contextPath}/guide?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加第一个导游
                    </a>
                </div>
            <%
                }
            %>
        </div>

        <!-- 分页导航 -->
        <% if (pageUtil != null && pageUtil.getTotalPages() > 1) { %>
            <div class="pagination-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted"><%= pageUtil.getDisplayInfo() %></small>
                    </div>
                    <nav>
                        <ul class="pagination mb-0">
                            <!-- 上一页 -->
                            <li class="page-item <%= !pageUtil.hasPrevious() ? "disabled" : "" %>">
                                <a class="page-link" href="<%= buildPageUrl(request, pageUtil.getPreviousPage()) %>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>

                            <!-- 页码 -->
                            <%
                                int[] pageRange = pageUtil.getPageRange();
                                for (int page : pageRange) {
                            %>
                                <li class="page-item <%= page == pageUtil.getCurrentPage() ? "active" : "" %>">
                                    <a class="page-link" href="<%= buildPageUrl(request, page) %>"><%= page %></a>
                                </li>
                            <%
                                }
                            %>

                            <!-- 下一页 -->
                            <li class="page-item <%= !pageUtil.hasNext() ? "disabled" : "" %>">
                                <a class="page-link" href="<%= buildPageUrl(request, pageUtil.getNextPage()) %>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        <% } %>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmDelete(id, name) {
            if (confirm('确定要删除导游 "' + name + '" 吗？此操作不可恢复！')) {
                window.location.href = '${pageContext.request.contextPath}/guide?action=delete&id=' + id;
            }
        }
    </script>

    <%!
        // 构建分页URL的辅助方法
        private String buildPageUrl(HttpServletRequest request, int page) {
            StringBuilder url = new StringBuilder();
            url.append(request.getContextPath()).append("/guide");

            String action = request.getParameter("action");
            String keyword = request.getParameter("keyword");
            String status = request.getParameter("status");
            String language = request.getParameter("language");
            String size = request.getParameter("size");

            if (action != null && !action.isEmpty()) {
                url.append("?action=").append(action);
            } else {
                url.append("?action=list");
            }

            if (keyword != null && !keyword.isEmpty()) {
                url.append("&keyword=").append(keyword);
            }

            if (status != null && !status.isEmpty()) {
                url.append("&status=").append(status);
            }

            if (language != null && !language.isEmpty()) {
                url.append("&language=").append(language);
            }

            if (size != null && !size.isEmpty()) {
                url.append("&size=").append(size);
            }

            url.append("&page=").append(page);

            return url.toString();
        }
    %>
</body>
</html>
