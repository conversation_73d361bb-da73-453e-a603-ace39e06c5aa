package com.tourism.dao;

import com.tourism.model.Guide;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 导游数据访问对象
 */
public class GuideDAO {
    
    /**
     * 添加导游
     */
    public boolean addGuide(Guide guide) {
        String sql = "INSERT INTO guides (name, id_card, phone, email, license_number, languages, specialties, experience, status, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql,
            guide.getName(),
            guide.getIdCard(),
            guide.getPhone(),
            guide.getEmail(),
            guide.getLicenseNumber(),
            guide.getLanguages(),
            guide.getSpecialties(),
            guide.getExperience(),
            guide.getStatus(),
            guide.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除导游
     */
    public boolean deleteGuide(int id) {
        String sql = "DELETE FROM guides WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新导游信息
     */
    public boolean updateGuide(Guide guide) {
        String sql = "UPDATE guides SET name=?, id_card=?, phone=?, email=?, license_number=?, languages=?, specialties=?, experience=?, status=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql,
            guide.getName(),
            guide.getIdCard(),
            guide.getPhone(),
            guide.getEmail(),
            guide.getLicenseNumber(),
            guide.getLanguages(),
            guide.getSpecialties(),
            guide.getExperience(),
            guide.getStatus(),
            guide.getDescription(),
            guide.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询导游
     */
    public Guide getGuideById(int id) {
        String sql = "SELECT * FROM guides WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractGuideFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有导游
     */
    public List<Guide> getAllGuides() {
        String sql = "SELECT * FROM guides ORDER BY experience DESC, name ASC";
        List<Guide> guides = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();

            while (rs.next()) {
                guides.add(extractGuideFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return guides;
    }
    
    /**
     * 根据名称搜索导游
     */
    public List<Guide> searchGuidesByName(String name) {
        String sql = "SELECT * FROM guides WHERE name LIKE ? ORDER BY experience DESC, name ASC";
        List<Guide> guides = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();

            while (rs.next()) {
                guides.add(extractGuideFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return guides;
    }
    
    /**
     * 根据状态查询导游
     */
    public List<Guide> getGuidesByStatus(String status) {
        String sql = "SELECT * FROM guides WHERE status = ? ORDER BY experience DESC, name ASC";
        List<Guide> guides = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();

            while (rs.next()) {
                guides.add(extractGuideFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return guides;
    }
    
    /**
     * 根据语言能力查询导游
     */
    public List<Guide> getGuidesByLanguage(String language) {
        String sql = "SELECT * FROM guides WHERE languages LIKE ? ORDER BY experience DESC, name ASC";
        List<Guide> guides = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + language + "%");
            rs = pstmt.executeQuery();

            while (rs.next()) {
                guides.add(extractGuideFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return guides;
    }
    
    /**
     * 检查导游证号是否已存在
     */
    public boolean isLicenseNumberExists(String licenseNumber, int excludeId) {
        String sql = "SELECT COUNT(*) FROM guides WHERE license_number = ? AND id != ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, licenseNumber);
            pstmt.setInt(2, excludeId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return false;
    }
    
    /**
     * 检查身份证号是否已存在
     */
    public boolean isIdCardExists(String idCard, int excludeId) {
        String sql = "SELECT COUNT(*) FROM guides WHERE id_card = ? AND id != ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, idCard);
            pstmt.setInt(2, excludeId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return false;
    }
    
    /**
     * 从ResultSet中提取Guide对象
     */
    private Guide extractGuideFromResultSet(ResultSet rs) throws SQLException {
        Guide guide = new Guide();
        guide.setId(rs.getInt("id"));
        guide.setName(rs.getString("name"));
        guide.setIdCard(rs.getString("id_card"));
        guide.setPhone(rs.getString("phone"));
        guide.setEmail(rs.getString("email"));
        guide.setLicenseNumber(rs.getString("license_number"));
        guide.setLanguages(rs.getString("languages"));
        guide.setSpecialties(rs.getString("specialties"));
        guide.setExperience(rs.getInt("experience"));
        guide.setStatus(rs.getString("status"));
        guide.setDescription(rs.getString("description"));
        return guide;
    }
}
