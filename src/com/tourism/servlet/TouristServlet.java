package com.tourism.servlet;

import com.tourism.dao.TouristDAO;
import com.tourism.model.Tourist;
import com.tourism.util.PageUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 游客管理Servlet
 */
public class TouristServlet extends HttpServlet {
    private TouristDAO touristDAO = new TouristDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listTourists(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteTourist(request, response);
                break;
            case "search":
                searchTourists(request, response);
                break;
            default:
                listTourists(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addTourist(request, response);
        } else if ("update".equals(action)) {
            updateTourist(request, response);
        }
    }
    
    /**
     * 显示游客列表（带分页）
     */
    private void listTourists(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // 获取分页参数
        int currentPage = 1;
        int pageSize = 10;
        
        String pageParam = request.getParameter("page");
        String sizeParam = request.getParameter("size");
        
        if (pageParam != null && !pageParam.isEmpty()) {
            try {
                currentPage = Integer.parseInt(pageParam);
            } catch (NumberFormatException e) {
                currentPage = 1;
            }
        }
        
        if (sizeParam != null && !sizeParam.isEmpty()) {
            try {
                pageSize = Integer.parseInt(sizeParam);
                if (pageSize < 5) pageSize = 5;
                if (pageSize > 50) pageSize = 50;
            } catch (NumberFormatException e) {
                pageSize = 10;
            }
        }
        
        // 创建分页对象
        PageUtil pageUtil = new PageUtil(currentPage, pageSize);
        
        // 获取总记录数
        int totalRecords = touristDAO.getTotalCount();
        pageUtil.setTotalRecords(totalRecords);
        
        // 获取当前页数据
        List<Tourist> tourists = touristDAO.getAllTourists(pageUtil);
        
        // 设置请求属性
        request.setAttribute("tourists", tourists);
        request.setAttribute("pageUtil", pageUtil);
        request.getRequestDispatcher("/tourist-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加游客表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.getRequestDispatcher("/tourist-add.jsp").forward(request, response);
    }
    
    /**
     * 添加游客
     */
    private void addTourist(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String address = request.getParameter("address");
        String customerType = request.getParameter("customerType");
        String preferences = request.getParameter("preferences");
        String emergencyContact = request.getParameter("emergencyContact");
        String emergencyPhone = request.getParameter("emergencyPhone");
        String remarks = request.getParameter("remarks");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/tourist?action=add");
            return;
        }
        
        // 检查身份证号是否已存在
        if (touristDAO.isIdCardExists(idCard, 0)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/tourist?action=add");
            return;
        }

        Tourist tourist = new Tourist(name, idCard, phone, email, address);
        tourist.setCustomerType(customerType != null ? customerType : "新客户");
        tourist.setPreferences(preferences);
        tourist.setEmergencyContact(emergencyContact);
        tourist.setEmergencyPhone(emergencyPhone);
        tourist.setRemarks(remarks);

        boolean success = touristDAO.addTourist(tourist);
        
        if (success) {
            request.getSession().setAttribute("message", "游客添加成功！");
        } else {
            request.getSession().setAttribute("error", "游客添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 显示编辑游客表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Tourist tourist = touristDAO.getTouristById(id);
        request.setAttribute("tourist", tourist);
        request.getRequestDispatcher("/tourist-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新游客信息
     */
    private void updateTourist(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String address = request.getParameter("address");
        String status = request.getParameter("status");
        String customerType = request.getParameter("customerType");
        String preferences = request.getParameter("preferences");
        String emergencyContact = request.getParameter("emergencyContact");
        String emergencyPhone = request.getParameter("emergencyPhone");
        String remarks = request.getParameter("remarks");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/tourist?action=edit&id=" + id);
            return;
        }
        
        // 检查身份证号是否已存在（排除当前记录）
        if (touristDAO.isIdCardExists(idCard, id)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/tourist?action=edit&id=" + id);
            return;
        }

        Tourist tourist = new Tourist();
        tourist.setId(id);
        tourist.setName(name);
        tourist.setIdCard(idCard);
        tourist.setPhone(phone);
        tourist.setEmail(email);
        tourist.setAddress(address);
        tourist.setStatus(status);
        tourist.setCustomerType(customerType);
        tourist.setPreferences(preferences);
        tourist.setEmergencyContact(emergencyContact);
        tourist.setEmergencyPhone(emergencyPhone);
        tourist.setRemarks(remarks);
        
        boolean success = touristDAO.updateTourist(tourist);
        
        if (success) {
            request.getSession().setAttribute("message", "游客信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "游客信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 删除游客
     */
    private void deleteTourist(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = touristDAO.deleteTourist(id);
        
        if (success) {
            request.getSession().setAttribute("message", "游客删除成功！");
        } else {
            request.getSession().setAttribute("error", "游客删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 搜索游客（带分页）
     */
    private void searchTourists(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String status = request.getParameter("status");
        
        // 获取分页参数
        int currentPage = 1;
        int pageSize = 10;
        
        String pageParam = request.getParameter("page");
        String sizeParam = request.getParameter("size");
        
        if (pageParam != null && !pageParam.isEmpty()) {
            try {
                currentPage = Integer.parseInt(pageParam);
            } catch (NumberFormatException e) {
                currentPage = 1;
            }
        }
        
        if (sizeParam != null && !sizeParam.isEmpty()) {
            try {
                pageSize = Integer.parseInt(sizeParam);
                if (pageSize < 5) pageSize = 5;
                if (pageSize > 50) pageSize = 50;
            } catch (NumberFormatException e) {
                pageSize = 10;
            }
        }
        
        // 创建分页对象
        PageUtil pageUtil = new PageUtil(currentPage, pageSize);
        
        List<Tourist> tourists;
        int totalRecords;
        
        if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            totalRecords = touristDAO.getCountByStatus(status);
            pageUtil.setTotalRecords(totalRecords);
            tourists = touristDAO.getTouristsByStatus(status, pageUtil);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            totalRecords = touristDAO.getSearchCountByName(keyword);
            pageUtil.setTotalRecords(totalRecords);
            tourists = touristDAO.searchTouristsByName(keyword, pageUtil);
        } else {
            // 显示所有
            totalRecords = touristDAO.getTotalCount();
            pageUtil.setTotalRecords(totalRecords);
            tourists = touristDAO.getAllTourists(pageUtil);
        }
        
        request.setAttribute("tourists", tourists);
        request.setAttribute("pageUtil", pageUtil);
        request.setAttribute("keyword", keyword);
        request.setAttribute("status", status);
        request.getRequestDispatcher("/tourist-list.jsp").forward(request, response);
    }
}
