<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Tourist" %>
<%@ page import="com.tourism.util.PageUtil" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .table-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .btn-group .btn { margin-right: 5px; }
        .search-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .status-badge { font-size: 0.8em; }
        .pagination-container { background: white; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); padding: 15px; margin-top: 20px; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users text-primary me-2"></i>游客管理</h2>
            <a href="${pageContext.request.contextPath}/tourist?action=add" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加游客
            </a>
        </div>

        <!-- 消息提示 -->
        <%
            String message = (String) session.getAttribute("message");
            if (message != null) {
        %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><%= message %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <%
                session.removeAttribute("message");
            }
            
            String error = (String) session.getAttribute("error");
            if (error != null) {
        %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <%
                session.removeAttribute("error");
            }
        %>

        <!-- 搜索区域 -->
        <div class="search-container">
            <form method="get" action="${pageContext.request.contextPath}/tourist">
                <input type="hidden" name="action" value="search">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">姓名搜索</label>
                        <input type="text" class="form-control" name="keyword" 
                               value="${param.keyword}" placeholder="输入游客姓名">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">状态筛选</label>
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="活跃" ${param.status == '活跃' ? 'selected' : ''}>活跃</option>
                            <option value="非活跃" ${param.status == '非活跃' ? 'selected' : ''}>非活跃</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">每页显示</label>
                        <select class="form-select" name="size">
                            <option value="10" ${param.size == '10' ? 'selected' : ''}>10条</option>
                            <option value="20" ${param.size == '20' ? 'selected' : ''}>20条</option>
                            <option value="50" ${param.size == '50' ? 'selected' : ''}>50条</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                            <a href="${pageContext.request.contextPath}/tourist" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 游客列表 -->
        <div class="table-container p-4">
            <%
                PageUtil pageUtil = (PageUtil) request.getAttribute("pageUtil");
                List<Tourist> tourists = (List<Tourist>) request.getAttribute("tourists");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            %>
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>游客列表
                </h5>
                <% if (pageUtil != null) { %>
                    <small class="text-muted"><%= pageUtil.getDisplayInfo() %></small>
                <% } %>
            </div>

            <%
                if (tourists != null && !tourists.isEmpty()) {
            %>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>身份证号</th>
                                <th>电话</th>
                                <th>地址</th>
                                <th>注册时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <%
                                for (Tourist tourist : tourists) {
                            %>
                                <tr>
                                    <td><%= tourist.getId() %></td>
                                    <td>
                                        <strong><%= tourist.getName() %></strong>
                                        <% if (tourist.getEmail() != null && !tourist.getEmail().isEmpty()) { %>
                                            <br><small class="text-muted"><%= tourist.getEmail() %></small>
                                        <% } %>
                                    </td>
                                    <td><%= tourist.getIdCard() %></td>
                                    <td><%= tourist.getPhone() %></td>
                                    <td>
                                        <% if (tourist.getAddress() != null && !tourist.getAddress().isEmpty()) { %>
                                            <%= tourist.getAddress().length() > 20 ? 
                                                tourist.getAddress().substring(0, 20) + "..." : 
                                                tourist.getAddress() %>
                                        <% } else { %>
                                            <span class="text-muted">未填写</span>
                                        <% } %>
                                    </td>
                                    <td>
                                        <% if (tourist.getRegisterDate() != null) { %>
                                            <%= sdf.format(tourist.getRegisterDate()) %>
                                        <% } %>
                                    </td>
                                    <td>
                                        <%
                                            String statusClass = "secondary";
                                            if ("活跃".equals(tourist.getStatus())) {
                                                statusClass = "success";
                                            } else if ("非活跃".equals(tourist.getStatus())) {
                                                statusClass = "warning";
                                            }
                                        %>
                                        <span class="badge bg-<%= statusClass %> status-badge">
                                            <%= tourist.getStatus() %>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="${pageContext.request.contextPath}/tourist?action=edit&id=<%= tourist.getId() %>" 
                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" 
                                               onclick="confirmDelete(<%= tourist.getId() %>, '<%= tourist.getName() %>')" 
                                               class="btn btn-sm btn-outline-danger" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <%
                                }
                            %>
                        </tbody>
                    </table>
                </div>
            <%
                } else {
            %>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无游客信息</h5>
                    <p class="text-muted">点击上方"添加游客"按钮开始添加游客信息</p>
                    <a href="${pageContext.request.contextPath}/tourist?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加第一个游客
                    </a>
                </div>
            <%
                }
            %>
        </div>

        <!-- 分页导航 -->
        <% if (pageUtil != null && pageUtil.getTotalPages() > 1) { %>
            <div class="pagination-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted"><%= pageUtil.getDisplayInfo() %></small>
                    </div>
                    <nav>
                        <ul class="pagination mb-0">
                            <!-- 上一页 -->
                            <li class="page-item <%= !pageUtil.hasPrevious() ? "disabled" : "" %>">
                                <a class="page-link" href="<%= buildPageUrl(request, pageUtil.getPreviousPage()) %>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            
                            <!-- 页码 -->
                            <%
                                int[] pageRange = pageUtil.getPageRange();
                                for (int page : pageRange) {
                            %>
                                <li class="page-item <%= page == pageUtil.getCurrentPage() ? "active" : "" %>">
                                    <a class="page-link" href="<%= buildPageUrl(request, page) %>"><%= page %></a>
                                </li>
                            <%
                                }
                            %>
                            
                            <!-- 下一页 -->
                            <li class="page-item <%= !pageUtil.hasNext() ? "disabled" : "" %>">
                                <a class="page-link" href="<%= buildPageUrl(request, pageUtil.getNextPage()) %>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        <% } %>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmDelete(id, name) {
            if (confirm('确定要删除游客 "' + name + '" 吗？此操作不可恢复！')) {
                window.location.href = '${pageContext.request.contextPath}/tourist?action=delete&id=' + id;
            }
        }
    </script>

    <%!
        // 构建分页URL的辅助方法
        private String buildPageUrl(HttpServletRequest request, int page) {
            StringBuilder url = new StringBuilder();
            url.append(request.getContextPath()).append("/tourist");
            
            String action = request.getParameter("action");
            String keyword = request.getParameter("keyword");
            String status = request.getParameter("status");
            String size = request.getParameter("size");
            
            if (action != null && !action.isEmpty()) {
                url.append("?action=").append(action);
            } else {
                url.append("?action=list");
            }
            
            if (keyword != null && !keyword.isEmpty()) {
                url.append("&keyword=").append(keyword);
            }
            
            if (status != null && !status.isEmpty()) {
                url.append("&status=").append(status);
            }
            
            if (size != null && !size.isEmpty()) {
                url.append("&size=").append(size);
            }
            
            url.append("&page=").append(page);
            
            return url.toString();
        }
    %>
</body>
</html>
