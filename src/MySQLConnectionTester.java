import java.sql.*;

public class MySQLConnectionTester {
    // 数据库配置参数
    private static final String JDBC_URL = "******************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "root";

    public static void main(String[] args) {
        testConnection();
    }

    public static void testConnection() {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;

        try {
            // 1. 加载驱动 (MySQL 8.0+ 可以省略这步)
            Class.forName("com.mysql.cj.jdbc.Driver");

            // 2. 建立连接
            System.out.println("尝试连接到数据库...");
            conn = DriverManager.getConnection(
                    JDBC_URL + "?useSSL=false&serverTimezone=UTC",
                    USERNAME,
                    PASSWORD
            );

            // 3. 测试连接是否成功
            if (conn != null) {
                System.out.println("✅ 数据库连接成功！");

                // 4. 创建测试表（如果不存在）
                stmt = conn.createStatement();
                stmt.executeUpdate("CREATE TABLE IF NOT EXISTS test_table ("
                        + "id INT AUTO_INCREMENT PRIMARY KEY, "
                        + "name VARCHAR(50), "
                        + "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

                // 5. 插入测试数据
                stmt.executeUpdate("INSERT INTO test_table (name) VALUES ('测试数据1')");
                stmt.executeUpdate("INSERT INTO test_table (name) VALUES ('测试数据2')");

                // 6. 查询数据
                System.out.println("\n执行查询测试...");
                rs = stmt.executeQuery("SELECT * FROM test_table");

                // 7. 显示结果
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // 打印表头
                for (int i = 1; i <= columnCount; i++) {
                    System.out.print(metaData.getColumnName(i) + "\t");
                }
                System.out.println();

                // 打印数据
                while (rs.next()) {
                    for (int i = 1; i <= columnCount; i++) {
                        System.out.print(rs.getString(i) + "\t");
                    }
                    System.out.println();
                }

                System.out.println("\n✅ 数据库测试完成！");
            }
        } catch (ClassNotFoundException e) {
            System.err.println("❌ MySQL 驱动未找到，请检查依赖：" + e.getMessage());
        } catch (SQLException e) {
            System.err.println("❌ 数据库操作失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 8. 清理资源
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                System.err.println("关闭资源时出错：" + e.getMessage());
            }
        }
    }
}
