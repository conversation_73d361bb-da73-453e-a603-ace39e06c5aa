<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.model.Tourist" %>
<%
    Tourist tourist = (Tourist) request.getAttribute("tourist");
    if (tourist == null) {
        response.sendRedirect(request.getContextPath() + "/tourist");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑游客 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .form-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .required { color: red; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/tourist">返回游客列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="form-container p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4><i class="fas fa-user-edit me-2"></i>编辑游客 - <%= tourist.getName() %></h4>
                        <a href="${pageContext.request.contextPath}/tourist" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </a>
                    </div>

                    <!-- 错误消息 -->
                    <%
                        String error = (String) session.getAttribute("error");
                        if (error != null) {
                    %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <%
                            session.removeAttribute("error");
                        }
                    %>

                    <form method="post" action="${pageContext.request.contextPath}/tourist" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" value="<%= tourist.getId() %>">
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-user me-2"></i>基本信息</h6>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">姓名 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<%= tourist.getName() != null ? tourist.getName() : "" %>" required>
                                    <div class="invalid-feedback">请输入游客姓名</div>
                                </div>

                                <div class="mb-3">
                                    <label for="idCard" class="form-label">身份证号 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="idCard" name="idCard" 
                                           value="<%= tourist.getIdCard() != null ? tourist.getIdCard() : "" %>"
                                           pattern="[0-9]{17}[0-9Xx]" maxlength="18" required>
                                    <div class="invalid-feedback">请输入正确的18位身份证号</div>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">联系电话 <span class="required">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<%= tourist.getPhone() != null ? tourist.getPhone() : "" %>"
                                           pattern="[0-9]{11}" maxlength="11" required>
                                    <div class="invalid-feedback">请输入正确的11位手机号</div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱地址</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<%= tourist.getEmail() != null ? tourist.getEmail() : "" %>">
                                    <div class="invalid-feedback">请输入正确的邮箱地址</div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">联系地址</label>
                                    <textarea class="form-control" id="address" name="address" rows="2" 
                                              placeholder="详细地址"><%= tourist.getAddress() != null ? tourist.getAddress() : "" %></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="活跃" <%= "活跃".equals(tourist.getStatus()) ? "selected" : "" %>>活跃</option>
                                        <option value="非活跃" <%= "非活跃".equals(tourist.getStatus()) ? "selected" : "" %>>非活跃</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 客户信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>客户信息</h6>
                                
                                <div class="mb-3">
                                    <label for="customerType" class="form-label">客户类型</label>
                                    <select class="form-select" id="customerType" name="customerType">
                                        <option value="新客户" <%= "新客户".equals(tourist.getCustomerType()) ? "selected" : "" %>>新客户</option>
                                        <option value="常客" <%= "常客".equals(tourist.getCustomerType()) ? "selected" : "" %>>常客</option>
                                        <option value="VIP客户" <%= "VIP客户".equals(tourist.getCustomerType()) ? "selected" : "" %>>VIP客户</option>
                                        <option value="商务客户" <%= "商务客户".equals(tourist.getCustomerType()) ? "selected" : "" %>>商务客户</option>
                                        <option value="家庭客户" <%= "家庭客户".equals(tourist.getCustomerType()) ? "selected" : "" %>>家庭客户</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="preferences" class="form-label">旅游偏好</label>
                                    <textarea class="form-control" id="preferences" name="preferences" rows="2" 
                                              placeholder="例如：历史文化、自然风光、美食体验等"><%= tourist.getPreferences() != null ? tourist.getPreferences() : "" %></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="emergencyContact" class="form-label">紧急联系人</label>
                                    <input type="text" class="form-control" id="emergencyContact" name="emergencyContact" 
                                           value="<%= tourist.getEmergencyContact() != null ? tourist.getEmergencyContact() : "" %>"
                                           placeholder="紧急联系人姓名">
                                </div>

                                <div class="mb-3">
                                    <label for="emergencyPhone" class="form-label">紧急联系电话</label>
                                    <input type="tel" class="form-control" id="emergencyPhone" name="emergencyPhone" 
                                           value="<%= tourist.getEmergencyPhone() != null ? tourist.getEmergencyPhone() : "" %>"
                                           pattern="[0-9]{11}" maxlength="11" placeholder="紧急联系人电话">
                                </div>

                                <div class="mb-3">
                                    <label for="remarks" class="form-label">备注信息</label>
                                    <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                              placeholder="其他补充信息"><%= tourist.getRemarks() != null ? tourist.getRemarks() : "" %></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="${pageContext.request.contextPath}/tourist" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>更新游客
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Bootstrap 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 身份证号格式化
        document.getElementById('idCard').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            e.target.value = value;
        });

        // 手机号格式验证
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });

        // 紧急联系电话格式验证
        document.getElementById('emergencyPhone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });
    </script>
</body>
</html>
