package com.tourism.dao;

import com.tourism.model.Tourist;
import com.tourism.util.DatabaseUtil;
import com.tourism.util.PageUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 游客数据访问对象
 */
public class TouristDAO {
    
    /**
     * 添加游客
     */
    public boolean addTourist(Tourist tourist) {
        String sql = "INSERT INTO tourists (name, id_card, phone, email, address, register_date, status, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            tourist.getName(), 
            tourist.getIdCard(), 
            tourist.getPhone(), 
            tourist.getEmail(), 
            tourist.getAddress(), 
            tourist.getRegisterDate(), 
            tourist.getStatus(), 
            tourist.getRemarks()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除游客
     */
    public boolean deleteTourist(int id) {
        String sql = "DELETE FROM tourists WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新游客信息
     */
    public boolean updateTourist(Tourist tourist) {
        String sql = "UPDATE tourists SET name=?, id_card=?, phone=?, email=?, address=?, status=?, remarks=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            tourist.getName(), 
            tourist.getIdCard(), 
            tourist.getPhone(), 
            tourist.getEmail(), 
            tourist.getAddress(), 
            tourist.getStatus(), 
            tourist.getRemarks(), 
            tourist.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询游客
     */
    public Tourist getTouristById(int id) {
        String sql = "SELECT * FROM tourists WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractTouristFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有游客（带分页）
     */
    public List<Tourist> getAllTourists(PageUtil pageUtil) {
        String sql = "SELECT * FROM tourists ORDER BY register_date DESC LIMIT ?, ?";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, pageUtil.getOffset());
            pstmt.setInt(2, pageUtil.getLimit());
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 查询所有游客（不分页）
     */
    public List<Tourist> getAllTourists() {
        String sql = "SELECT * FROM tourists ORDER BY register_date DESC";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 获取游客总数
     */
    public int getTotalCount() {
        String sql = "SELECT COUNT(*) FROM tourists";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 根据姓名搜索游客（带分页）
     */
    public List<Tourist> searchTouristsByName(String name, PageUtil pageUtil) {
        String sql = "SELECT * FROM tourists WHERE name LIKE ? ORDER BY register_date DESC LIMIT ?, ?";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            pstmt.setInt(2, pageUtil.getOffset());
            pstmt.setInt(3, pageUtil.getLimit());
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 获取按姓名搜索的游客总数
     */
    public int getSearchCountByName(String name) {
        String sql = "SELECT COUNT(*) FROM tourists WHERE name LIKE ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 根据状态查询游客（带分页）
     */
    public List<Tourist> getTouristsByStatus(String status, PageUtil pageUtil) {
        String sql = "SELECT * FROM tourists WHERE status = ? ORDER BY register_date DESC LIMIT ?, ?";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            pstmt.setInt(2, pageUtil.getOffset());
            pstmt.setInt(3, pageUtil.getLimit());
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 获取按状态查询的游客总数
     */
    public int getCountByStatus(String status) {
        String sql = "SELECT COUNT(*) FROM tourists WHERE status = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 检查身份证号是否已存在
     */
    public boolean isIdCardExists(String idCard, int excludeId) {
        String sql = "SELECT COUNT(*) FROM tourists WHERE id_card = ? AND id != ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, idCard);
            pstmt.setInt(2, excludeId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return false;
    }
    
    /**
     * 从ResultSet中提取Tourist对象
     */
    private Tourist extractTouristFromResultSet(ResultSet rs) throws SQLException {
        Tourist tourist = new Tourist();
        tourist.setId(rs.getInt("id"));
        tourist.setName(rs.getString("name"));
        tourist.setIdCard(rs.getString("id_card"));
        tourist.setPhone(rs.getString("phone"));
        tourist.setEmail(rs.getString("email"));
        tourist.setAddress(rs.getString("address"));
        tourist.setRegisterDate(rs.getTimestamp("register_date"));
        tourist.setStatus(rs.getString("status"));
        tourist.setRemarks(rs.getString("remarks"));
        return tourist;
    }
}
